import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from '@/store';

// 问卷答题  
export function dailyQuestion(data) {
    return request({
        url: `${process.env.VUE_APP_URL}/action/study/dailyTrainingRecord/dailyQuestion?currentUsername=${data.currentUsername}`,
        contentType: 'application/json;charset=UTF-8',
        loading:true
    })
}
// 错题集合
export function findByUsername(data) {
    return request({
        url: `${process.env.VUE_APP_URL}/action/study/wrongQuestionCollection/findByUsername?source=SMS&page=${data.page}&rows=${data.size}&size=${data.size}`,
        contentType: 'application/json;charset=UTF-8',
        data: data
    })
}

// 根据id查询错题信息
export function findQuestionById(data) {
    return request({
        url: `${process.env.VUE_APP_URL}/action/study/wrongQuestionCollection/findQuestionById?id=${data.id}`,
        contentType: 'application/json;charset=UTF-8',
    })
}
// 提交每题信息接口
export function submitAnswer(data) {
    return request({
        url: `${process.env.VUE_APP_URL}/action/study/dailyTrainingRecord/submitAnswer`,
        contentType: 'application/json;charset=UTF-8',
        data: data
    })
}
// 错题上报
export function saveModify(data) {
    return request({
        url: `${process.env.VUE_APP_URL}/action/modifyReport/saveModify`,
        contentType: 'application/json;charset=UTF-8',
        data: data
    })
}

// 错题删除
export function deleteById(data) {
    return request({
        url: `${process.env.VUE_APP_URL}/action/study/wrongQuestionCollection/deleteById?id=${data.id}`,
        contentType: 'application/json;charset=UTF-8',
    })
}





