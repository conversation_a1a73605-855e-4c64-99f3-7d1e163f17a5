<template>
  <div class="main">
    <div class="title" @click="$router.back()">
      <i class="iconfont iconfanhui font16 fl pl06"><van-icon name="arrow-left" /></i>
      <span class="font17 ">返回</span>
    </div>
    <div class="main-content" ref="scrollRef">
      <div class="row" v-for="item in errlist" @click="godetail(item)">
        <div class="top">
          <div class="questionContent">{{item.questionContent}}</div>
          <van-button type="danger" size="small" style="margin-right: 5px;"
            @click.stop="sendErr(item)">上报异常</van-button>
          <van-button type="danger" size="small" @click.stop="remove(item)">移除</van-button>
        </div>
        <div class="bto"><span>最近答错日期<span style="margin-left: 5px;">{{item.lastWrongTime}}</span></span>
          <span>累计答错次数<span style="margin-left: 5px;">{{item.wrongCount}}</span></span>
        </div>
      </div>
    </div>
    <!-- 确认弹出层 -->
    <van-popup v-model:show="showCenter" :style="{ width: '80%', borderRadius:'5px', padding: '20px' }">
      <div class="maintext">
        <div class="titles">请选择您觉得对的答案</div>
        <div style="flex:1;overflow-y: auto;">
          <div class="subject" v-for="(item, i) in questionAll" :key="i">
            <div class="question-type">
              {{item.questionType === 'judge' ? '判断题' : item.questionType === 'more' ? '多选题' :
              item.questionType === 'single' ? '单选题' : ''}}
            </div>
            <div class="question">{{i+1}}、{{ item.questionName }}</div>
            <!-- 单选题 -->
            <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]" direction="horizontal">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" style="width: 100%;" class="radio-item">
                <van-radio :name="its.answerCode" icon-size="18px">
                  <span>{{
                    its.answerContent
                    }}</span>
                </van-radio>
              </div>
            </van-radio-group>
            <!-- 多选题 -->
            <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" class="checkbox-item">
                <van-checkbox shape="square" :name="its.answerCode" icon-size="18px"> <span>{{ its.answerCode
                    +'、'
                    +its.answerContent }}</span> </van-checkbox>
              </div>
            </van-checkbox-group>
            <!-- 判断题 -->
            <van-radio-group v-if="item.questionType === 'judge'" v-model="result[i]">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" class="radio-item">
                <van-radio :name="its.answerCode" icon-size="18px"><span v-html="its.answerContent"></span></van-radio>
              </div>
            </van-radio-group>
          </div>
        </div>
      </div>
      <div>
        <van-button type="primary" size="small" style="width: 100%;background-color: red;border: unset;"
          @click="save">提交</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
  import store from "@/store";
  import { Dialog, } from "vant";
  import { Notify } from "vant";
  import { findByUsername, findQuestionById, saveModify, deleteById } from '@/api/asksurveyJjzsjs.js'
  export default {
    name: "asksurvey",
    data() {
      return {
        parms: {
          currentUsername: store.state.user.username,
          page: 1,
          size: 10,
        },
        totalPages: 0,
        isEnd: false,
        isScrolledToEnd: false,
        errlist: [],
        showCenter: false,
        showRow: {},
        questionAll: [],
        number: 0,
        result: [], // 提交结果
        showAnswerColor: false
      };
    },
    mounted() {
      var that = this
      this.$refs.scrollRef.addEventListener('scroll', function (val) {
        if (that.totalPages <= that.parms.page) {
          return
        }
        let scrollTop = val.target.scrollTop
        let clientHeight = val.target.clientHeight
        let scrollHeight = val.target.scrollHeight
        if (scrollTop + clientHeight >= scrollHeight - 10) {
          if (that.isScrolledToEnd) {
            return
          }
          that.parms.page = that.parms.page + 1
          // 请求新数据
          that.isScrolledToEnd = true
          that.getNewData()
        }

      })
    },
    created() {
      this.getNewData()
    },
    activated() {
    },
    methods: {
      getNewData() {
        findByUsername({ ...this.parms }).then(res => {
          this.totalPages = res.data.totalPages
          this.errlist = [...this.errlist, ...res.data.content]
          setTimeout(() => {
            this.isScrolledToEnd = false
          }, 500);
        })
      },
      godetail(val) {
        this.$router.push('/asksurveyJjzsjsTest?id=' + val.id)
      },
      sendErr(row) {
        Dialog.confirm({
          title: '提示',
          message: '是否上报异常该题目？',
        }).then(() => {
          saveModify({
            questionCode: row.questionCode,
          }).then(res => {
            Notify({ type: 'success', message: '提交成功' });
            // this.showCenter = false
          })
        })

        // findQuestionById({
        //   id: row.id,
        //   currentUsername: store.state.user.username
        // }).then(res => {
        //   let allquestion = res.data.question
        //   if (allquestion.questionType == "judge") {
        //     allquestion.answerList.forEach(ele2 => {
        //       if (ele2.answerCode == 'false') {
        //         ele2.answerContent = 'false'
        //       } else {
        //         ele2.answerContent = 'true'
        //       }
        //     })
        //   }
        //   allquestion.correctAnswer = res.data.correctAnswer
        //   this.showAnswerColor = true
        //   this.$set(this.questionAll, 0, allquestion)
        //   this.result = []
        //   this.showCenter = true
        // })
      },
      remove(row) {
        deleteById({
          id: row.id
        }).then(res => {
          Notify({ type: 'success', message: '移除成功' });
          this.errlist = this.errlist.filter(item => item.id !== row.id)
        })
      },
      save() {

      }
    }
  };
</script>

<style scoped>
  .row {
    border-bottom: 1px solid #797979;
    margin-bottom: 5px;
    padding-bottom: 5px;
  }

  .top {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    color: #333333;
    /* 溢出部分显示省略号 */
    width: 100%;
    /* 需要指定宽度或最大宽度 */
    margin-bottom: 5px;
  }

  .questionContent {
    flex: 1;
    white-space: nowrap;
    /* 禁止文字换行 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
  }

  .bto {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }

  .main {
    height: 100vh;
    width: 100%;
    background-image: url('../../../assets/images/jjzsjs/u0.jpg');
    background-size: 100% 100%;
    position: relative;
  }

  .main-content {
    position: absolute;
    height: 60%;
    width: 90%;
    bottom: 100px;
    left: 50%;
    transform: translate(-50%);
    display: flex;
    background-color: rgba(255, 255, 255, 0.898039215686275);
    flex-direction: column;
    border-radius: 10px;
    padding: 20px 10px;
    overflow-y: auto;
  }


  .title {

    color: #fff;
    line-height: .4rem;
    padding: 0 .16rem 0 .16rem;
    height: .4rem;
    background-color: #c50f0b;
    text-align: left;
    width: 100%;
  }

  .van-button--small {
    height: 22px;
  }

  .question {
    margin-bottom: 10px;
  }

  .titles {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .checkbox-item {
    margin-bottom: 10px;
  }

  .radio-item {
    margin-bottom: 10px;
  }
</style>