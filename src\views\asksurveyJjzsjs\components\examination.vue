<template>
  <div class="main">
    <div class="title" @click="$router.back()">
      <i class="iconfont iconfanhui font16 fl pl06"><van-icon name="arrow-left" /></i>
      <span class="font17 ">返回</span>
    </div>
    <div class="main-number" v-if="showSubmit">
      <div class="now-word">
        <span style="font-size: 20px;font-weight: 600;">{{number+1}}</span>
        <span style="font-size: 16px;"> / {{questionAll.length}}</span>
      </div>
      <div class="bg-line">
        <div class="line" :style="{width: getNowWidth()}"></div>
      </div>
      <div class="next-word">
        <div style="height: 60px;display: flex;justify-content: space-between" v-if="showSubmit">
          <div round block type="text" @click.stop="sendErr(questionAll[number])" >试题纠错</div>
          <div style="display: flex;">
            <div round block type="text" @click.stop="previous" v-show="number + 1 > 1">上一题</div>
            <div round block type="text" :disabled="seconds>0" @click.stop="handleNext" style="margin-left: 10px">
              {{ number + 1 == this.questionAll.length ? '提交': '下一题' }}
              <span v-show="seconds>0">({{seconds}}秒后跳转)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="main-content">
      <div class="maintext">
        <div style="flex:1;overflow-y: auto;">
          <div class="subject" v-show="number == i" v-for="(item, i) in questionAll" :key="i">
            <div class="question-type">
              {{item.questionType === 'judge' ? '判断题' : item.questionType === 'more' ? '多选题' :
                item.questionType === 'single' ? '单选题' : ''}}
            </div>
            <div class="question">{{i+1}}、{{ item.questionName }}</div>
            <!-- 单选题 -->
            <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]" direction="horizontal">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" style="width: 100%;" class="radio-item">
                <van-radio :disabled="disabled" :name="its.answerCode" icon-size="18px">
                                    <span :style="{
                                        color: (its.answerCode == questionAll[number]?.correctAnswer && showAnswerColor )? 'green': result[i] != questionAll[number]?.correctAnswer && its.answerCode == result[i] && showAnswerColor ? 'red' : '#000'
                                    }">{{ its.answerContent }}</span>
                </van-radio>
              </div>
            </van-radio-group>
            <!-- 多选题 -->
            <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" class="checkbox-item">
                <van-checkbox shape="square" :disabled="disabled" :name="its.answerCode" icon-size="18px">
                                    <span :style="{color: getColor(its,result[i])}">
                                        {{ its.answerCode +'、'+ its.answerContent }}
                                    </span>
                </van-checkbox>
              </div>
            </van-checkbox-group>
            <!-- 判断题 -->
            <van-radio-group v-if="item.questionType === 'judge'" v-model="result[i]">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" class="radio-item">
                <van-radio :disabled="disabled" :name="its.answerCode" icon-size="18px">
                                    <span :style="{
                                        color: (its.answerCode == questionAll[number]?.correctAnswer && showAnswerColor )? 'green': result[i] != questionAll[number]?.correctAnswer && its.answerCode == result[i] && showAnswerColor ? 'red' : '#000'
                                    }" v-html="its.answerContent"></span>
                </van-radio>
              </div>
            </van-radio-group>
          </div>
        </div>
        <div style="height: 60px;margin-bottom: 10px;" v-show="showAnswer">
                    <span style="color: green;">
                        正确答案：{{questionAll[number]?.correctAnswer == 'true' ? '对' : questionAll[number]?.correctAnswer == 'false' ? '错' : questionAll[number]?.correctAnswer}}
                    </span>
        </div>
      </div>
    </div>
    <!-- 输入弹窗 -->
    <van-dialog v-model:show="showDialog" title="试题纠错"  :before-close="beforeClose" show-cancel-button>
      <van-field v-model="modifyContent" label="说明" placeholder="请输入" type="textarea"  rows="1" autosize colon label-width="4.5em"/>
    </van-dialog>
  </div>
</template>

<script>
import store from "@/store";
import {Dialog, Toast} from "vant";
import { Notify } from "vant";
import {dailyQuestion, findQuestionById, saveModify, submitAnswer} from '@/api/asksurveyJjzsjs.js'

export default {
  name: "asksurvey",
  data() {
    return {
      examAppCode: this.$route.query.examAppCode ? this.$route.query.examAppCode : '',
      examCode: this.$route.query.examCode ? this.$route.query.examCode : '',
      questionAll: [], // 全部试题
      result: [], // 提交结果
      number: 0, // 当前题码
      showAnswer: false, // 是否显示底部答案
      showSubmit: true, // 是否展示保存按钮
      disabled: false, // 是否禁止选择
      showAnswerColor: false, // 是否显示答案颜色
      seconds: 0, // 倒计时
      timer: null,
      previousBtn: false,
      xzNum: '',
      answeredQuestions: [], // 记录已答题目索引
      submitted: false, // 是否已提交
      showDialog:false,
      modifyContent:''
    };
  },
  mounted() {
    // 判断是否为错题查看
    if (this.$route.query && this.$route.query.id) {
      this.showAnswer = true
      this.showSubmit = false
      this.disabled = true
      // 获取单一
      this.getById(this.$route.query.id)
    } else {
      if (this.$route.query && this.$route.query.nowNumber) {
        // 如果有nowNumber参数，设置当前题码
        this.number = Number(this.$route.query.nowNumber)
      }
      // 获取所有
      this.getList()
      // document.addEventListener('visibilitychange', this.handleVisibilityChange);
      // window.addEventListener('beforeunload', this.handleBeforeUnload);
      // 定时检测窗口尺寸变化
      // this.windowCheckInterval = setInterval(this.checkWindowSize, 1000);
    }
  },
  beforeDestroy() {
    // document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    // window.removeEventListener('beforeunload', this.handleBeforeUnload);
    // clearInterval(this.windowCheckInterval);
  },
  methods: {
    // 显示Toast提示
    showToast(message) {
      Toast({
        message: message,
        duration: 1500
      });
    },
    sendErr(row) {
      if (!row) return;
      this.showDialog = true
    },
    beforeClose(action,done) {
      if (action === 'cancel') {
        // 点击取消按钮时直接关闭弹窗
        done()
        return;
      }
      // 点击确认按钮时的处理
      if (this.modifyContent) {
        saveModify({
          questionCode: this.questionAll[this.number]?.questionCode, // 修复了 row 未定义的问题
          modifyContent: this.modifyContent
        }).then(res => {
          Notify({ type: 'success', message: '提交成功' });
          this.showDialog = false; // 手动关闭弹窗
          this.modifyContent = ''; // 清空输入内容
        }).catch(error => {
          Notify({ type: 'danger', message: '提交失败' });
        });
        done()
      } else {
        this.showToast('请填写说明！');
        done(false)
      }
    },
    // 上一题
    previous() {
      if (this.number > 0) {
        this.number--;
        // this.updateQuestionState();
        this.showAnswer = true
        this.showAnswerColor = true
        this.disabled = true
        this.clearTimer();
      }
    },

    // 处理下一题/提交
    handleNext() {
      if (this.number + 1 === this.questionAll.length) {
        // 如果是最后一题，执行提交
        this.submit();
      } else {
        // 否则只切换到下一题
        this.goToNextQuestion();
      }
    },

    // 单纯的切换到下一题
    goToNextQuestion() {
      if (this.timer) return;
      if (!this.answeredQuestions.includes(this.number) && !this.showAnswer) {
        // 如果当前题目未答，先提交
        this.submit();
        return;
      }

      this.number++;
      this.updateQuestionState();
    },

    // 提交答案
    submit() {
      if (this.timer || this.submitted) return;

      let parms = {
        questionId: this.questionAll[this.number].id,
        recordId: this.questionAll[this.number].answerRecordId,
        userAnswer: !this.result[this.number] ? '' :
            typeof this.result[this.number] == 'string' ?
                this.result[this.number] :
                this.result[this.number].sort().join(','),
        seq: this.number + 1
      };

      this.showAnswer = true;
      this.showAnswerColor = true;
      this.disabled = true;

      if (!this.answeredQuestions.includes(this.number)) {
        this.answeredQuestions.push(this.number);
      }

      this.submitted = true;
      submitAnswer({ ...parms }).finally(() => {
        this.submitted = false;
      });

      if (this.number + 1 == this.questionAll.length) {
        this.showSubmit = false;
        this.$router.push('/asksurveyJjzsjsFinish');
      } else {
        this.seconds = 1;
        this.timer = setInterval(() => {
          this.seconds--;
          if (this.seconds <= 0) {
            this.clearTimer();
            this.goToNextQuestion();
          }
        }, 1000);
      }
    },

    // 更新题目状态
    updateQuestionState() {
      this.showAnswer = this.answeredQuestions.includes(this.number) || this.number < Number(this.$route.query.nowNumber);
      this.showAnswerColor = this.answeredQuestions.includes(this.number) || this.number < Number(this.$route.query.nowNumber);
      this.disabled = this.answeredQuestions.includes(this.number) || this.number < Number(this.$route.query.nowNumber);
    },

    // 清除计时器
    clearTimer() {
      clearInterval(this.timer);
      this.timer = null;
      this.seconds = 0;
    },

    getNowWidth() {
      let onePice = 100 / this.questionAll.length / 100
      return (this.number + 1) * onePice * 100 + '%'
    },

    // handleVisibilityChange() {
    //   if (document.visibilityState === 'hidden') {
    //     this.submit()
    //   }
    // },

    // handleBeforeUnload(e) {
    //   this.submit()
    // },

    // checkWindowSize() {
    //   if (document.visibilityState !== 'visible') return;
    //   const w = window.innerWidth;
    //   const h = window.innerHeight;
    //   const sw = window.screen.width;
    //   const sh = window.screen.height;
    //   const widthRatio = w / sw;
    //   const heightRatio = h / sh;
    //   if ((widthRatio < 0.7 || heightRatio < 0.7) && !this.submitted) {
    //     this.submit();
    //     alert('检测到可能进入了小窗/画中画/悬浮窗模式，已自动提交！');
    //   }
    //   this.lastWidth = w;
    //   this.lastHeight = h;
    // },

    getColor(its, result) {
      if (!this.showAnswerColor) return '#000';
      if (this.questionAll[this.number]?.correctAnswer.includes(its.answerCode)) {
        return 'green';
      } else {
        return (result && result.includes(its.answerCode)) ? 'red' : '#000';
      }
    },

    getById(id) {
      findQuestionById({
        id,
        currentUsername: store.state.user.username
      }).then(res => {
        let allquestion = res.data.question
        if (allquestion.questionType == "judge") {
          allquestion.answerList.forEach(ele2 => {
            if (ele2.answerCode == 'false') {
              ele2.answerContent = '错'
            } else {
              ele2.answerContent = '对'
            }
          })
        }
        allquestion.correctAnswer = res.data.correctAnswer
        this.showAnswerColor = true
        this.$set(this.questionAll, 0, allquestion)
        this.result = res.data.userAnswer ? [res.data.userAnswer] : []
      })
    },

    getList() {
      // let data = {
      //   currentUsername: store.state.user.username
      // }
      // dailyQuestion(data).then(res => {
        this.questionAll =  JSON.parse(localStorage.getItem('questions')) || []
        if (!this.questionAll || !this.questionAll.length) return

        this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);

        // 数据序号处理
        const typeIndex = {};
        this.questionAll.forEach(obj => {
          const { type } = obj;
          if (typeIndex.hasOwnProperty(type)) {
            typeIndex[type] += 1;
            obj.index = typeIndex[type];
          } else {
            typeIndex[type] = 1;
            obj.index = typeIndex[type];
          }
        });

        this.questionAll.forEach(item => {
          item.correctAnswer = null
          let answer = []
          let ans = item.answerList.filter(ele => ele.isCorrect)
          ans.forEach(ele => answer.push(ele.answerCode))
          item.correctAnswer = answer.join(',')

          if (item.questionType == "judge") {
            item.answerList.forEach(ele2 => {
              ele2.answerContent = ele2.answerCode == 'false' ? '错' : '对'
            })
          }
        })
    //   })
    }
  }
};
</script>

<style scoped>
.main {
  height: 100vh;
  width: 100%;
  background-color: #d1d1d169;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.main-number {
  height: 90px;
  padding: 20px 20px;
}

.main-content {
  flex: 7;
  display: flex;
  justify-content: center;
  margin: 20px 20px;
  align-items: center;
  padding: 10px 0px;
  background-color: #fff;
  border-radius: 10px;
}

.maintext {
  width: 100%;
  margin-left: .3rem;
  margin-right: .3rem;
  display: flex;
  height: 100%;
  flex-direction: column;
}

.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start;
}

.subject {
  margin-bottom: 0.2rem;
}

.question {
  line-height: 0.22rem;
  margin-bottom: 0.1rem;
  font-weight: 700;
}

.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}

.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}

.textDec p {
  text-indent: 2em;
}

.van-radio__label {
  width: 40px;
}

.title {
  color: #fff;
  line-height: .4rem;
  padding: 0 .16rem 0 .16rem;
  height: .4rem;
  background-color: #c50f0b;
  text-align: left;
  width: 100%;
}

.now-word {}

.question-type {
  border-left: 3px solid #c7423c;
  padding-left: 10px;
  margin: 10px 0px;
  font-weight: 600;
  font-size: 16px;
}

.next-word {
  text-align: right;
  color: #c7423c;
  font-size: 18px;
  font-weight: 600;
}

.bg-line {
  height: 10px;
  width: 100%;
  background-color: #d1d1d1;
  border-radius: 5px;
  margin: 10px 0px;
}

.line {
  height: 10px;
  width: 100%;
  background-color: gold;
  border-radius: 5px;
  margin: 10px 0px;
}

.radio-item {
  background: #f7f8fa;
  padding: 5px;
  margin-bottom: 8px;
}

.checkbox-item {
  background: #f7f8fa;
  padding: 5px;
  margin-bottom: 8px;
}

.van-radio {
  margin-bottom: 0px;
}
</style>