<template>
  <div class="main">
    <div class="title" @click="$router.back()">
      <i class="iconfont iconfanhui font16 fl pl06"><van-icon name="arrow-left" /></i>
      <span class="font17 ">返回</span>
    </div>
    <div class="main-number" v-if="showSubmit">
      <div class="next-word" style="display: flex;justify-content: space-between;align-items: center">
        <div style="display: flex" v-if="questionAll[number]">
          <div style="margin-right: 10px;" @click.stop="sendErr(questionAll[number])">试题纠错</div>
          <div @click.stop="remove(questionAll[number])">移除</div>
        </div>
        <div style="display: flex;justify-content: flex-end" v-if="showSubmit">
          <div @click.stop="previous" v-show="number + 1 > 1">上一题</div>
          <div @click.stop="handleNext" style="margin-left: 10px">
            下一题
          </div>
        </div>
      </div>
    </div>
    <div class="main-content">
      <div class="maintext">
        <div style="flex:1;overflow-y: auto;">
          <div class="subject" v-show="number == i && item" v-for="(item, i) in questionAll" :key="i">
            <div class="question-type" v-if="item">
              {{item.questionType === 'judge' ? '判断题' : item.questionType === 'more' ? '多选题' :
                item.questionType === 'single' ? '单选题' : ''}}
            </div>
            <div class="question" v-if="item">{{i+1}}、{{ item.questionName }}</div>
            <!-- 单选题 -->
            <van-radio-group v-if="item && item.questionType === 'single'" v-model="result[i]" direction="horizontal">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" style="width: 100%;" class="radio-item">
                <van-radio :disabled="disabled" :name="its.answerCode" icon-size="18px">
                  <span :style="{
                    color: (its.answerCode == questionAll[number]?.correctAnswer && showAnswerColor )? 'green': result[i] != questionAll[number]?.correctAnswer && its.answerCode == result[i] && showAnswerColor ? 'red' : '#000'
                  }">{{ its.answerContent }}</span>
                </van-radio>
              </div>
            </van-radio-group>
            <!-- 多选题 -->
            <van-checkbox-group v-if="item && item.questionType === 'more'" v-model="result[i]">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" class="checkbox-item">
                <van-checkbox shape="square" :disabled="disabled" :name="its.answerCode" icon-size="18px">
                  <span :style="{color: getColor(its,result[i])}">
                    {{ its.answerCode +'、'+ its.answerContent }}
                  </span>
                </van-checkbox>
              </div>
            </van-checkbox-group>
            <!-- 判断题 -->
            <van-radio-group v-if="item && item.questionType === 'judge'" v-model="result[i]">
              <div v-for="(its,indexs) in item.answerList" :key="indexs" class="radio-item">
                <van-radio :disabled="disabled" :name="its.answerCode" icon-size="18px">
                  <span :style="{
                    color: (its.answerCode == questionAll[number]?.correctAnswer && showAnswerColor )? 'green': result[i] != questionAll[number]?.correctAnswer && its.answerCode == result[i] && showAnswerColor ? 'red' : '#000'
                  }" v-html="its.answerContent"></span>
                </van-radio>
              </div>
            </van-radio-group>
          </div>
        </div>
<!--        <div class="top" style="text-align: end" v-if="questionAll[number]">-->
<!--          <van-button type="danger" size="small" style="margin-right: 5px;"-->
<!--                      @click.stop="sendErr(questionAll[number])">试题纠错</van-button>-->
<!--          <van-button type="danger" size="small" @click.stop="remove(questionAll[number])">移除</van-button>-->
<!--        </div>-->
        <div style="height: 60px;margin-bottom: 10px;" v-show="showAnswer && questionAll[number]">
          <span style="color: green;">
            正确答案：{{questionAll[number]?.correctAnswer == 'true' ? '对' : questionAll[number]?.correctAnswer == 'false' ? '错' : questionAll[number]?.correctAnswer}}
          </span>
        </div>
      </div>
    </div>

    <!-- 空数据提示弹窗 -->
    <van-dialog
        v-model="showEmptyDialog"
        title="提示"
        @confirm="goBack"
    >
      <div style="text-align: center; padding: 20px;">
        {{emptyMessage}}
      </div>
    </van-dialog>
    <!-- 输入弹窗 -->
    <van-dialog v-model:show="showDialog" title="试题纠错"  :before-close="beforeClose" show-cancel-button>
      <van-field v-model="modifyContent" label="说明" placeholder="请输入" type="textarea"  rows="1" autosize colon label-width="4.5em"/>
    </van-dialog>
  </div>
</template>

<script>
import store from "@/store";
import {deleteById, findByUsername, findQuestionById, saveModify} from '@/api/asksurveyJjzsjs.js'
import {Dialog, Notify, Toast} from 'vant';

export default {
  name: "examinationErr",
  data() {
    return {
      questionAll: [], // 全部试题
      result: [], // 提交结果
      number: 0, // 当前题码
      showAnswer: true, // 是否显示底部答案
      showSubmit: true, // 是否展示保存按钮
      disabled: true, // 是否禁止选择
      showAnswerColor: true, // 是否显示答案颜色
      showEmptyDialog: false, // 是否显示空数据弹窗
      emptyMessage: '暂无错题数据', // 空数据提示信息
      parms: {
        currentUsername: store.state.user.username,
        page: 1,
        size: 10, // 每页数量调整为较小值
      },
      errlist: [], // 错误题目ID列表
      loading: false, // 加载状态
      hasMore: true, // 是否还有更多数据
      preloadCount: 3, // 预加载阈值
      showDialog:false,
      modifyContent:''
    };
  },
  mounted() {
    this.getNewData();
  },
  methods: {
    // 显示Toast提示
    showToast(message) {
      Toast({
        message: message,
        duration: 1500
      });
    },

    async getNewData() {
      if (this.loading || !this.hasMore) return;
      this.loading = true;
      try {
        const res = await findByUsername({ ...this.parms });

        if (res.data.content.length === 0) {
          this.hasMore = false;

          // 如果是第一次加载且没有数据，显示提示
          if (this.parms.page === 1 && this.errlist.length === 0) {
            this.showEmptyDialog = true;
          }
          return;
        }

        // 合并新数据
        const newIds = res.data.content.map(item => item.id);
        this.errlist = [...this.errlist, ...newIds];

        // 预加载第一批题目
        if (this.questionAll.length === 0 && this.errlist.length > 0) {
          await this.preloadQuestions(0, Math.min(5, this.errlist.length));
        }

        // 准备加载下一页
        this.parms.page++;
      } catch (error) {
        this.emptyMessage = '加载数据失败，请稍后重试';
        this.showEmptyDialog = true;
      } finally {
        this.loading = false;
      }
    },

    // 预加载多个题目
    async preloadQuestions(startIndex, count) {
      const promises = [];
      const endIndex = Math.min(startIndex + count, this.errlist.length);
      for (let i = startIndex; i < endIndex; i++) {
        if (!this.questionAll[i]) {
          promises.push(this.loadQuestion(i));
        }
      }
      await Promise.all(promises);
    },

    // 加载单个题目
    async loadQuestion(index) {
      try {
        if (!this.errlist[index]) return;

        const res = await findQuestionById({
          id: this.errlist[index],
          currentUsername: store.state.user.username
        });

        if (!res.data || !res.data.question) return;

        let allquestion = res.data.question;

        if (allquestion.questionType == "judge") {
          allquestion.answerList.forEach(ele2 => {
            ele2.answerContent = ele2.answerCode == 'false' ? '错' : '对';
          });
        }

        allquestion.correctAnswer = res.data.correctAnswer;

        // 使用Vue.set确保响应式更新
        this.$set(this.questionAll, index, allquestion);
        this.$set(this.result, index, res.data.userAnswer || '');

      } catch (error) {
        console.error(`加载题目${index}失败:`, error);
        this.emptyMessage = '加载题目失败，请稍后重试';
        this.showEmptyDialog = true;
      }
    },

    // 上一题
    async previous() {
      if (this.number > 0) {
        this.number--;
        // 如果当前题目还没有加载，先加载
        if (!this.questionAll[this.number]) {
          await this.loadQuestion(this.number);
        }
      }
    },

    // 处理下一题/提交
    async handleNext() {
      if (this.number < this.errlist.length - 1) {
        this.number++;

        // 预加载下一批数据
        if (this.number + this.preloadCount >= this.errlist.length && this.hasMore) {
          await this.getNewData();
        }

        // 预加载接下来的几个题目
        if (this.number + 2 < this.errlist.length) {
          await this.preloadQuestions(this.number + 1, 2);
        }

        // 如果当前题目还没有加载，先加载
        if (!this.questionAll[this.number]) {
          await this.loadQuestion(this.number);
        }
      } else if (this.hasMore) {
        // 尝试加载更多数据
        await this.getNewData();
        if (this.number < this.errlist.length - 1) {
          this.number++;
          await this.loadQuestion(this.number);
        } else {
          // 如果没有更多数据了，隐藏下一题按钮并显示提示
          this.showToast('已经是最后一题了');
        }
      } else {
        // 已经是最后一题了，隐藏下一题按钮并显示提示
        this.showToast('已经是最后一题了');
      }
    },

    getColor(its, result) {
      if (!this.showAnswerColor) return '#000';
      if (this.questionAll[this.number]?.correctAnswer.includes(its.answerCode)) {
        return 'green';
      } else {
        return (result && result.includes(its.answerCode)) ? 'red' : '#000';
      }
    },

    // 返回上一页
    goBack() {
      this.$router.back();
    },

    sendErr(row) {
      if (!row) return;
      this.showDialog = true
    },
    beforeClose(action,done) {
      if (action === 'cancel') {
        // 点击取消按钮时直接关闭弹窗
        done()
        return;
      }
      // 点击确认按钮时的处理
      if (this.modifyContent) {
        saveModify({
          questionCode: this.questionAll[this.number]?.questionCode, // 修复了 row 未定义的问题
          modifyContent: this.modifyContent
        }).then(res => {
          Notify({ type: 'success', message: '提交成功' });
          this.showDialog = false; // 手动关闭弹窗
          this.modifyContent = ''; // 清空输入内容
        }).catch(error => {
          Notify({ type: 'danger', message: '提交失败' });
        });
        done()
      } else {
        this.showToast('请填写说明！');
        done(false)
      }
    },

    remove(row) {
      if (!row) return;

      Dialog.confirm({
        title: '提示',
        message: '确定要移除该题目吗？',
      }).then(() => {
        deleteById({
          id: this.errlist[this.number]
        }).then(res => {
          Notify({ type: 'success', message: '移除成功' });

          // 过滤数组
          this.questionAll = this.questionAll.filter(item => item && item.id !== row.id);
          this.errlist = this.errlist.filter(item => item !== this.errlist[this.number]);

          // 处理索引问题
          if (this.number >= this.questionAll.length && this.number > 0) {
            this.number--;
          }

          // 如果已经没有题目了，显示空状态
          if (this.questionAll.length === 0) {
            this.showEmptyDialog = true;
          } else {
            this.handleNext();
          }
        }).catch(error => {
          Notify({ type: 'danger', message: '移除失败' });
        });
      }).catch(() => {});
    },
  }
};
</script>

<style scoped>
.main {
  height: 100vh;
  width: 100%;
  background-color: #d1d1d169;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.main-number {
  height: 10px;
  padding: 20px 20px;
}

.main-content {
  flex: 7;
  display: flex;
  justify-content: center;
  margin: 20px 20px;
  align-items: center;
  padding: 10px 0px;
  background-color: #fff;
  border-radius: 10px;
}

.maintext {
  width: 100%;
  margin-left: .3rem;
  margin-right: .3rem;
  display: flex;
  height: 100%;
  flex-direction: column;
}

.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start;
}

.subject {
  margin-bottom: 0.2rem;
}

.question {
  line-height: 0.22rem;
  margin-bottom: 0.1rem;
  font-weight: 700;
}

.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}

.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}

.textDec p {
  text-indent: 2em;
}

.van-radio__label {
  width: 40px;
}

.title {
  color: #fff;
  line-height: .4rem;
  padding: 0 .16rem 0 .16rem;
  height: .4rem;
  background-color: #c50f0b;
  text-align: left;
  width: 100%;
}

.now-word {}

.question-type {
  border-left: 3px solid #c7423c;
  padding-left: 10px;
  margin: 10px 0px;
  font-weight: 600;
  font-size: 16px;
}

.next-word {
  text-align: right;
  color: #c7423c;
  font-size: 18px;
  font-weight: 600;
}

.bg-line {
  height: 10px;
  width: 100%;
  background-color: #d1d1d1;
  border-radius: 5px;
  margin: 10px 0px;
}

.line {
  height: 10px;
  width: 100%;
  background-color: gold;
  border-radius: 5px;
  margin: 10px 0px;
}

.radio-item {
  background: #f7f8fa;
  padding: 5px;
  margin-bottom: 8px;
}

.checkbox-item {
  background: #f7f8fa;
  padding: 5px;
  margin-bottom: 8px;
}

.van-radio {
  margin-bottom: 0px;
}
</style>