<template>
  <div class="finish-container">
    <div class="back" @click="$router.push('/asksurveyJjzsjs')">返回</div>
    <div class="red-background">
      <div class="title-container">
        <h1>完成练习！</h1>
<!--        <p>继续加油练习获得更多积分~</p>-->
      </div>
    </div>
    <div class="stats-container">
      <div class="top-stats">
        <div class="top-stat-item">
          <span class="top-value">{{zql}} %</span>
          <span class="top-label">正确率</span>
        </div>
        <div class="top-stat-item">
          <span class="top-value">{{TimeStr}}</span>
          <span class="top-label">用时</span>
        </div>
      </div>
      <div class="stat-item">
        <span class="label">答题数</span>
        <span class="value"> <span style="font-size: 24px;margin-right: 5px;">{{totalQuestions}}</span>题</span>
      </div>
      <div class="stat-item">
        <span class="label">答对</span>
        <span class="value"><span style="font-size: 24px;margin-right: 5px;">{{sucQuestions}}</span>题</span>

      </div>
      <!-- <div class="stat-item">
        <span class="label">积分</span>
        <span class="value"> <span style="font-size: 24px;margin-right: 5px;">+0.0</span>分</span>
      </div> -->
      <button class="again-button" @click="onAgainClick">再来一次</button>
    </div>
  </div>
</template>

<script>
  import { dailyQuestion, } from '@/api/asksurveyJjzsjs.js'
  import store from "@/store";
  export default {
    name: 'FinishPage',
    data() {
      return {
        zql: 0,
        TimeStr: 0,
        sucQuestions: 0,
        totalQuestions: 0,
      }
    },

    methods: {
      onAgainClick() {
        // 在这里添加再来一次的逻辑
        // 例如，重新开始练习或返回首页
        this.$router.push('/asksurveyJjzsjs');
      },

    },
    mounted() {
      let data = {
        currentUsername: store.state.user.username
      }

      dailyQuestion(data)
          .then(res => {
            if (!res.data || !res.data.record) {
              throw new Error('Invalid response data');
            }

            const recod = res.data.record;

            // 计算正确率
            this.zql = recod.totalQuestions > 0
                ? ((recod.correctQuestions / recod.totalQuestions) * 100).toFixed(2)
                : 0;

            // 计算用时
            try {
              const dateStr = recod.createdTime.replace(/-/g, '/');
              let secondA = new Date(dateStr);
              const secondB = new Date();
              const totalSecond = Math.abs(Math.floor((secondB - secondA) / 1000));
              const minutes = Math.floor(totalSecond / 60);
              const second = totalSecond % 60;
              this.TimeStr = minutes + '分' + second + '秒';
            } catch (e) {
              console.error('Error calculating time:', e);
              this.TimeStr = '0分0秒';
            }

            this.totalQuestions = recod.totalQuestions || 0;
            this.sucQuestions = recod.correctQuestions || 0;
          })
          .catch(err => {
            console.error('Failed to get daily question data:', err);
            // 可以在这里设置默认值或显示错误提示
            this.zql = 0;
            this.TimeStr = '0分0秒';
            this.totalQuestions = 0;
            this.sucQuestions = 0;
          });
    }
  }
</script>

<style scoped>
  .finish-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    font-family: 'Arial', sans-serif;
    position: relative;
  }

  .red-background {
    background-color: #d54431;
    color: white;
    width: 100%;
    height: 100%;
    padding: 40px 20px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;
  }

  .red-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
      radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
    background-size: 100% 100%;
  }

  .title-container {
    position: relative;
    z-index: 1;
    text-align: left;
    margin-top: 20px;
  }

  h1 {
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: bold;
  }

  .title-container p {
    font-size: 14px;
    opacity: 0.9;
  }



  .stats-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.05);
    width: 90%;
    max-width: 500px;
    margin-top: 150px;
    padding: 40px 20px 40px;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    width: 100%;
  }

  .top-stats {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 30px;
    padding-bottom: 20px;
  }

  .top-stat-item {
    text-align: center;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .top-label {
    color: #999;
    font-size: 14px;
    display: block;
    margin-bottom: 5px;
  }

  .top-value {
    color: #f93e3e;
    font-size: 28px;
    font-weight: bold;
  }

  .stat-item:last-child {
    border-bottom: none;
  }

  .label {
    color: #666;
    font-size: 16px;
  }

  .value {
    color: #333;
    font-size: 18px;
    font-weight: bold;
  }

  .again-button {
    background-color: #d54431;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: bold;
    margin-top: 30px;
    cursor: pointer;
    width: 100%;
    box-shadow: 0 5px 15px rgba(249, 62, 62, 0.3);
    transition: all 0.3s ease;
  }

  .again-button:hover {
    background-color: #e53535;
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(229, 53, 53, 0.3);
  }

  .again-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(229, 53, 53, 0.3);
  }

  .back {
    position: absolute;
    left: 20px;
    top: 20px;
    z-index: 9;
    color: red;
    font-size: 18px;
    padding: 5px 10px;
    font-weight: 600;
    background: #ffffff;
    border-radius: 20px;
  }
</style>