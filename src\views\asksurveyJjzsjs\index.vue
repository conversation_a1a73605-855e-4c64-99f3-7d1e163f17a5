<template>
  <div class="main">
    <div class="main-content">
      <div class="top" @click="rjdz">
        每日人机对战
      </div>
      <div class="bto" @click="ctj">
        错题集
      </div>
      <!-- 确认弹出层 -->
      <van-popup v-model:show="showCenter" :style="{ width: '60%', borderRadius:'5px', padding: '20px 10px' }">
        每日人机对战每人每天可参与{{allNumber}}次，今日您已参与{{canedNum}}次，还有{{ allNumber - canedNum}}次机会
        <div style="text-align: center;"><van-button @click="startTest" type="danger" size="small"
            v-if="hasNum">开始答题</van-button>
        </div>
      </van-popup>
    </div>
  </div>
</template>
<script>
  import store from "@/store";
  import { Dialog, showNotify, Notify, Toast } from "vant";
  import { dailyQuestion } from '@/api/asksurveyJjzsjs.js'
  export default {
    name: "asksurvey",
    data() {
      return {
        showCenter: false,
        allNumber: 0,
        canedNum: 4,
        hasNum: true,
        recoder: {}
      };
    },
    mounted() {
    },
    created() {
      // this.getTimes()
    },
    activated() {
    },
    methods: {
      // 查询答题次数
      getTimes() {
        dailyQuestion({
          currentUsername:  store.state.user.username
        }).then(res => {
          //  共有次数
          this.allNumber = res.data.allNumber
          // 已答次数
          this.canedNum = res.data.nowNumber
          // 能否继续答题
          this.hasNum = res.data.hasNum
          this.recoder = res.data.record
          localStorage.setItem('questions', JSON.stringify(res.data.questions));
        })
      },
      rjdz() {
        const toast = Toast.loading({
          duration: 3000, // 持续展示 toast
          forbidClick: true,
          message: '加载中...',
        });
        dailyQuestion({
          currentUsername:  store.state.user.username
        }).then(res => {
          //  共有次数
          this.allNumber = res.data.allNumber
          // 已答次数
          this.canedNum = res.data.nowNumber
          // 能否继续答题
          this.hasNum = res.data.hasNum
          this.recoder = res.data.record
          localStorage.setItem('questions', JSON.stringify(res.data.questions));
          if (this.hasNum) {
            // this.showCenter = true
            Toast.clear();
            this.startTest()
          } else {
            Dialog.alert({
              title: "",
              message: "当前答题次数已用尽！",
            }).then(() => {
              window.close()
            });
            Toast.clear();
          }
        })
      },
      startTest() {
        this.showCenter = false
        this.$router.push('/asksurveyJjzsjsTest?nowNumber=' + this.recoder.nowSeq)

      },
      ctj() {
        // this.$router.push('/asksurveyJjzsjsErr')
        this.$router.push('/examinationErr')
      }
    }
  };
</script>

<style scoped>
  .main {
    height: 100vh;
    width: 100%;
    background-image: url('../../assets/images/jjzsjs/u0.jpg');
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .main-content {
    margin-top: 50px;
    height: 50%;
    width: 80%;
    display: flex;
    flex-direction: column;
  }

  .top {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url('../../assets/images/jjzsjs/u2.png');
    background-size: cover;
    opacity: 0.8;
    border: 2px solid #fff;
    border-radius: 10px;
    font-size: 28px;
    color: #fff;
  }

  .bto {
    margin-top: 20px;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url('../../assets/images/jjzsjs/u2.png');
    background-size: cover;
    opacity: 0.8;
    /* 也可用于文字颜色 */

    border: 2px solid #fff;
    border-radius: 10px;
    font-size: 28px;
    color: #fff;
  }
</style>