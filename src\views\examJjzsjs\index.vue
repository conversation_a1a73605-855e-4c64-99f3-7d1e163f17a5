<template>
  <div class="maintext">
    <div class="textDec">
      <h3>{{ examName }}</h3>
      <p v-html="examRemark"></p>

      <!-- <h3>{{ examName }}</h3> -->
      <!-- <p> 您好！根据省公司党委安排，现组织对许昌分公司党委开展民主测评和内部巡察问卷调查。本问卷不记名，由系统统一进行发放、回收和统计，严格保密。请根据您了解的情况，实事求是作答，避免漏答。感谢您的支持和参与！</p> -->
    </div>
    <div v-if="!isFinishExam && initTime > 0" class="djs">
      <van-icon name="clock" color="#1989fa" />
      <van-count-down
       ref="countDown"
        :time="initTime"
        format="时间剩余：HH:mm:ss"
        @change="timeChange"
      />
    </div>

    <div class="subject" v-for="(item, i) in questionAll" :key="i">
      <!-- <div class="Tit">{{item.questionGroupName}}</div> -->
      <!-- 题型描述 -->
      <div v-if="shouldShowQuestionTypeDesc(i, item.questionType)" class="question-type-desc">
        <h4 v-if="item.questionType === 'single'">单选题</h4>
        <h4 v-else-if="item.questionType === 'more'">多选题</h4>
        <h4 v-else-if="item.questionType === 'judge'">判断题</h4>
        <h4 v-else-if="item.questionType === 'shortAnswer'">简答题</h4>
      </div>
      <div class="question">{{ i + 1 + "、" + item.questionName }}</div>
      <!-- 单选题 -->
      <van-radio-group
      :disabled="isFinishExam"
        v-if="item.questionType === 'single'"
        v-model="result[i]"
        :class="'styleMY' + i"
      >
        <div v-for="(its, indexs) in item.answerList" :key="indexs">
          <van-radio :name="its.answerCode" icon-size="18px">{{
            its.answerCode + "、" + its.answerContent
          }}</van-radio>
        </div>
        <van-field
          autosize
          class="custom-field"
          v-if="
            item.answerList[funZM(result[i])]
              ? item.answerList[funZM(result[i])].identification == '1'
              : false
          "
          rows="3"
          type="textarea"
          v-model="zdytext[i]"
          placeholder="请填写"
        >
        </van-field>
      </van-radio-group>
      <!-- 多选题 -->
      <van-checkbox-group
      :disabled="isFinishExam"
        v-if="item.questionType === 'more'"
        v-model="result[i]"
      >
        <div v-for="(its, indexs) in item.answerList" :key="indexs">
          <van-checkbox
            shape="square"
            :name="its.answerCode"
            icon-size="18px"
            :disabled="its.disabled"
            @click="checkFun(item, its, i, indexs)"
            ref="checkboxes"
            >{{ its.answerCode + "、" + its.answerContent }}</van-checkbox
          >
        </div>
        <!-- 优化说明框：只显示一个，且在最后一个需要说明的选项后 -->
        <template v-if="result[i] && result[i].length">
          <van-field
            v-if="
              item.answerList[funZM(result[i][result[i].length - 1])] &&
              item.answerList[funZM(result[i][result[i].length - 1])]
                .identification == '1'
            "
            autosize
            class="custom-field"
            rows="3"
            type="textarea"
            v-model="zdytext[i]"
            placeholder="请填写"
          ></van-field>
        </template>
      </van-checkbox-group>
      <!-- 判断题 -->
      <van-radio-group v-if="item.questionType === 'judge'" :disabled="isFinishExam" v-model="result[i]">
        <div v-for="(its, indexs) in item.answerList" :key="indexs">
          <van-radio :name="its.answerCode" i icon-size="18px">{{
            its.answerContent
          }}</van-radio>
        </div>
      </van-radio-group>

      <!-- 简答题 -->
      <van-field
      :disabled="isFinishExam"
        v-if="item.questionType === 'shortAnswer'"
        autosize
        class="custom-field"
        v-model="result[i]"
        rows="3"
        type="textarea"
        placeholder="请填写"
      ></van-field>
    </div>

    <div style="margin: 16px;padding-bottom: 20px;" >
      <van-button round block type="info" v-if="!isFinishExam && startExam" @click="submit">提交</van-button>
    </div>

    <!-- <p style="text-align: center;color: #0000c7;text-decoration:underline;"> 如需反馈其他人员的问题或者问题线索请发至巡察组联系信箱*********************。</p> -->
  </div>
</template>
<script>
import store from "@/store";
import { Dialog } from "vant";
import { Notify, Toast } from "vant";
import {
  constructExamLayout,
  findExamInfo,
  submitExam,
  findEffectiveExamByExamCode,
} from "@/api/homes.js";

export default {
  name: "asksurvey",
  data() {
    return {
      examAppCode: this.$route.query.examAppCode
        ? this.$route.query.examAppCode
        : "",
      examCode: this.$route.query.examCode ? this.$route.query.examCode : "",
      singleQuestionList: [], //单选
      moreQuestionList: [], //多选
      judgeQuestionList: [], //判断题
      shortAnswerQuestionList: [], //简答
      questionAll: {}, //全部试题
      zdytext: [], //自定义文本
      result: [], //提交结果
      time: 10000, //时间
      initTime: 0, //进入考试总时长（毫秒）
      setTime: 0, //剩余考试时长（秒）
      usedTime: 0, //已用考试时长（秒）
      isFinishExam: false,

      examAnswer: [],
      examRecord: [],
      stime: 0,
      id: "",
      IntX: "",
      truename: store.state.user.user.truename, //姓名
      examName: "", //名称
      examRemark: "", //简介
      resultOrg: [], //原始提交结果；

      duoxunList: [],
      myindex: "",
      xh: 0,
       submitted: false,
      windowCheckInterval: null,
      lastWidth: window.innerWidth,
      lastHeight: window.innerHeight,
      startExam: false,

    };
  },
  mounted() {
     document.addEventListener('visibilitychange', this.handleVisibilityChange);
      window.addEventListener('beforeunload', this.handleBeforeUnload);
        // 窗口失焦 / 聚焦（辅助）
      window.addEventListener('blur', this.handleWindowBlur)
      // 定时检测窗口尺寸变化
      this.windowCheckInterval = setInterval(this.checkWindowSize, 1000);
  },
  created() {
    // this.getList();
    this.gettime();
  },
  beforeDestroy() {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    window.removeEventListener('blur', this.handleWindowBlur)
    if (this.windowCheckInterval) clearInterval(this.windowCheckInterval);
  },
  // activated() {
  //   this.gettime();
  // },
  methods: {
      handleWindowBlur() {
        if(this.startExam && !this.isFinishExam){
          this.saveTimefun('1');
        }
      },

     handleVisibilityChange() {
      if (document.visibilityState === 'hidden') {
        if(this.startExam && !this.isFinishExam){
         this.saveTimefun('1');
        }
      }
    },
    handleBeforeUnload(e) {
        if(this.startExam && !this.isFinishExam){
          this.saveTimefun('1');
            e.preventDefault();
        }
     
    },
    checkWindowSize() {
      if (document.visibilityState !== 'visible') return;
      const w = window.innerWidth;
      const h = window.innerHeight;
      const sw = window.screen.width;
      const sh = window.screen.height;
      const widthRatio = w / sw;
      const heightRatio = h / sh;
      // 阈值可根据实际体验调整，通常30%~40%较为合适
      if ((widthRatio < 0.7 || heightRatio < 0.7) && !this.submitted) {
         if(this.startExam && !this.isFinishExam){
         this.saveTimefun('1');

         }
        
      }
      this.lastWidth = w;
      this.lastHeight = h;
    },
    // 多选单机事件
    checkFun(item, its, i, indexs) {
      // specialType==01互斥逻辑
      // 1. 选中specialType==01的选项，只能选它，其他禁用且取消勾选
      if (its.specialType === "01") {
        if (this.result[i] && this.result[i].includes(its.answerCode)) {
          // 只保留当前选项为选中
          this.result[i] = [its.answerCode];
          item.answerList.forEach((ans) => {
            ans.disabled = ans.answerCode !== its.answerCode;
          });
          this.zdytext[i] = null; // 切换时清空说明
        } else {
          // 取消选中specialType==01，全部恢复可选
          item.answerList.forEach((ans) => {
            ans.disabled = false;
          });
          this.zdytext[i] = null; // 切换时清空说明
        }
      } else {
        // 2. 普通选项被选中时，如有specialType==01已选，则不允许选中普通选项，需先手动取消01选项
        let hasSpecial01 = false;
        let special01Code = "";
        item.answerList.forEach((ans) => {
          if (ans.specialType === "01") {
            special01Code = ans.answerCode;
            if (this.result[i] && this.result[i].includes(ans.answerCode)) {
              hasSpecial01 = true;
            }
          }
        });
        if (hasSpecial01) {
          // 已选中specialType==01，禁止选中普通选项，弹出提示
          this.$nextTick(() => {
            this.result[i] = [special01Code];
          });
          return;
        }
        // 恢复所有选项可选
        item.answerList.forEach((ans) => {
          ans.disabled = false;
        });
        this.zdytext[i] = null; // 切换时清空说明
      }
      // 最大选择数限制和排序逻辑保持不变
      if (item.maxChooseNum && this.result[i]?.length > item.maxChooseNum) {
        for (let i in this.duoxunList) {
          if (this.duoxunList[i].id == its.id) {
            this.myindex = i;
          }
        }
        this.$refs.checkboxes[this.myindex].toggle();
        this.result[i] = this.result[i].slice(0, this.result[i].length - 1);
        return Notify({
          type: "warning",
          message: "最多选择" + item.maxChooseNum + "项",
        });
      } else {
        this.result = this.sortNestedArrays(this.result);
      }
    },
    funZM(str) {
      //字母转换
      if (str) {
        return JSON.stringify(str).charCodeAt(1) - 65;
      }
    },
    // 字母排序
    sortNestedArrays(array) {
      return array.map((subArray) => {
        if (Array.isArray(subArray)) {
          return subArray.sort((a, b) => a.localeCompare(b));
        } else {
          return subArray;
        }
      });
    },
    getList() {
      let data = { examAppCode: this.examAppCode };
      constructExamLayout(data).then((res) => {
        Dialog.confirm({
          title: "温馨提示",
          message:
            "" +
           res.data.setTime +
            "分钟后自动交卷，答题过程中请勿关闭或刷新页面，您可以点击“提交”按钮交卷，请确认是否开始正式答题？",
        })
          .then(() => {
            this.startExam = true;
             this.initTime = parseFloat(res.data.setTime) * 60 * 1000;
            this.examName = res.data.examName;
            //  this.examRemark = res.data.examRemark
            this.singleQuestionList = res.data.singleQuestionList; //单选
            this.moreQuestionList = res.data.moreQuestionList; //多选
            this.shortAnswerQuestionList = res.data.shortAnswerQuestionList; //简答
            this.judgeQuestionList = res.data.judgeQuestionList; //判断题
            this.questionAll = this.singleQuestionList.concat(
              this.moreQuestionList,
              this.judgeQuestionList,
              this.shortAnswerQuestionList
            );
            this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);
            let xh = 0;
            for (let i in this.questionAll) {
              if (this.questionAll[i].questionGroupName) {
                xh = 1;
              }
              this.questionAll[i].xh = xh++;
            }

            this.duoxunList = this.moreQuestionList
              .map((a) => {
                return a.answerList.map((b) => b);
              })
              .flat();

            let Record = "";
            for (var i = 0; i < this.questionAll.length; i++) {
              Record += this.questionAll[i].questionCode + ",";
            }
            this.examRecord = Record.substring(0, Record.length - 1);
          })
          .catch(() => {});
      });
    },
    gettime() {
      //获取考试信息
      // 判断是否可以开始答题
      findEffectiveExamByExamCode({ examCodes: this.examCode })
        .then((res) => {
          // if (res.data.showFlag === true) {
          let data = {
            examAppCode: this.examAppCode,
            examCode: this.examCode,
            publishUsername: store.state.user.user.username,
          };
          findExamInfo(data).then((res) => {
            if (res.data) {
              if (!res.data.isFinishExam && !res.data.isMarkingExam) {
                this.getList();
              } else {
                this.isFinishExam = true;
                Dialog.alert({
                  title: "",
                  message: "您已完成竞赛答题，感谢您的参与！",
                }).then(() => {
                  window.close();
                });
              }
            } else {
              this.getList();
            }
          });
          // } else {
          //   Dialog.alert({
          //     title: "",
          //     message: "竞赛不在考试时间范围或者暂未权限！",
          //   }).then(() => {
          //     window.close();
          //     window.open("about:blank", "_self");
          //   });
          // }
        })
        .catch(() => {
          Dialog.alert({
            title: "",
            message: "竞赛不在考试时间范围或者暂未权限！",
          }).then(() => {
            window.close();
            window.open("about:blank", "_self");
          });
        });
    },
    // 倒计时
    timeChange(timeData) {
      if (!this.isFinishExam) {
        var sTime = parseInt(
          timeData.hours * 60 * 60 + timeData.minutes * 60 + timeData.seconds
        ); //秒
        this.setTime = sTime;
        // 计算已用时长 = 总时长(毫秒转秒) - 剩余时长(秒)
        this.usedTime = Math.floor(this.initTime / 1000) - sTime;
        if (
          this.setTime > 0 &&
          this.initTime != this.setTime * 1000 &&
          sTime % 60 == 0
        ) {
          //5秒间隔
          // this.saveTimefun('2'); //保存考题及答案
        }
        // 倒计时为0时也提交
        if (this.setTime == 0 && this.startExam && !this.isFinishExam) {
          this.saveTimefun('2');
        }
      }
    },
    saveTimefun(status){
      console.log('剩余时长(秒):', this.setTime)
      console.log('已用时长(秒):',this.formatTime(this.usedTime))
      if(status){
        if(status == '1'){
           Notify({
            type: "warning",
            message: "检测到有离开操作，将自动提交试卷！",
          });
        }else if(status == '2'){
           Notify({
            type: "warning",
            message: "考试时间到，将自动提交试卷！",
          });
        }
      }
       for (var i in this.result) {
            if (typeof this.result[i] == "string") {
              if (this.zdytext[i]) {
                if (
                  this.questionAll[i].answerList[this.funZM(this.result[i])] &&
                  this.questionAll[i].answerList[this.funZM(this.result[i])]
                    .identification == "1"
                ) {
                  this.result[i] = this.result[i] + ":" + this.zdytext[i];
                }
              }
            } else if (typeof this.result[i] == "object") {
              if (this.zdytext[i]) {
                let index = this.result[i].length - 1;
                this.result[i][index] =
                  this.result[i][index] + ":" + this.zdytext[i];
              }
            }
          }

          let aboutResult = [];
          for (var j in this.result) {
            if (typeof this.result[j] !== "string") {
              aboutResult[j] = this.result[j].join("/");
            } else {
              aboutResult[j] = this.result[j];
            }
          }

          let data = {
            examAppCode: this.examAppCode,
            examCode: this.examCode,
            publishUsername: store.state.user.user.username,
            residueTime: this.usedTime * 1000,
            // examAnswer: aboutResult.toString(),
            examRecord: this.examRecord,
          };

          let arrs = [];
          for (let u in this.questionAll) {
            if (this.questionAll[u].questionType == "more") {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                maxChooseNum: this.questionAll[u].maxChooseNum,
                examAnswer: aboutResult[u],
              });
            } else {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                examAnswer: aboutResult[u],
              });
            }
          }
          var newAnser = []
          data.examInfoList = arrs;
          for(let i in arrs){
            newAnser.push(arrs[i].examAnswer?arrs[i].examAnswer:'')
          }
          data.examAnswer = newAnser.toString();
          console.log(data,'data');
          Toast.loading({
            message: "加载中...",
            forbidClick: true,
            duration: 0,
          });
          submitExam(data).then((res) => {
            Toast.clear();
            if (res.status == 200) {
               this.isFinishExam = true;
                this.startExam = false;
                var sorce = res.data.score?res.data.score:0
            Dialog.alert({
                title: "",
                message: "您已完成竞赛答题，感谢您的参与！\n共得分：" + sorce + "分，用时：" + this.formatTime(this.usedTime),
              }).then(() => {
                 clearInterval(this.IntX);
               
                this.zdytext = [];
                this.djsdiv = false;
                window.close();
              });
            }
          }).catch(() => {
            Toast.clear();
          })


    },
    // 判断是否显示题型描述（每种题型的第一题显示）
    shouldShowQuestionTypeDesc(index, questionType) {
      if (index === 0) return true; // 第一题总是显示
      // 检查当前题目类型是否与前一题不同
      return this.questionAll[index - 1].questionType !== questionType;
    },
    // 格式化时间显示（秒转换为分钟秒）
    formatTime(seconds) {
      if (seconds < 60) {
        return seconds + "秒";
      } else {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        if (remainingSeconds === 0) {
          return minutes + "分钟";
        } else {
          return minutes + "分钟" + remainingSeconds + "秒";
        }
      }
    },
    submit() {
      let arr = [];
      for (let i in this.result) {
        if (this.result[i].length > 0) {
          arr.push(this.result[i]);
        }
      }

      if (arr.length !== this.questionAll.length) {
        return Notify({
          type: "warning",
          message: "您有未完成的题目，请继续填写！",
        });
      }

      for (var i in this.result) {
        if (typeof this.result[i] == "string") {
          if (this.zdytext[i]) {
          } else {
            if (
              this.questionAll[i].answerList[this.funZM(this.result[i])] &&
              this.questionAll[i].answerList[this.funZM(this.result[i])]
                .identification == "1"
            ) {
              return Notify({
                type: "warning",
                message: "您有未完成的题目，请填写具体说明！",
              });
            }
          }
        } else if (typeof this.result[i] == "object") {
          if (this.zdytext[i]) {
          } else {
            for (var v in this.result[i]) {
              if (
                this.questionAll[i].answerList[this.funZM(this.result[i][v])]
                  .identification == "1"
              ) {
                return Notify({
                  type: "warning",
                  message: "您有未完成的题目，请填写具体说明！",
                });
              }
            }
          }
        }
      }
      Dialog.confirm({
        title: "温馨提示",
        message: "您已完成了所有题目，请确认是否进行提交",
        confirmButtonColor: "#1989fa",
      })
        .then(() => {
          this.saveTimefun();
        })
        .catch(() => {});
    },
  },
};
</script>

<style>
.maintext {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}
.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start;
}
.subject {
  margin-bottom: 0.2rem;
}
.djs {
  position: fixed;
  top: 0.8rem;
  right: 0.3rem;
  background: #fff;
  display: flex;
  align-items: center;
  z-index: 2;
}
.question {
  line-height: 0.22rem;
  margin-bottom: 0.1rem;
  font-weight: 700;
}
.custom-field {
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding: 5px;
}
.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}
.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}
.textDec p {
  text-indent: 2em;
}

.bigTit div {
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 700;
}

.styleMY1 {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
}

/* .styleMY1>div{
  width: 50%;
} */

.Tit {
  font-weight: 700;
  font-size: 17px;
  margin-bottom: 10px;
}

.question-type-desc {
  background-color: #f0f8ff;
  border-left: 4px solid #1989fa;
  padding: 10px 15px;
  margin: 15px 0 10px 0;
  border-radius: 4px;
}

.question-type-desc h4 {
  margin: 0;
  color: #1989fa;
  font-size: 14px;
  font-weight: 600;
}
</style>
